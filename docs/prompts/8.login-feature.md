I want to implement a simple login feature for the an-com-dashboard application with the following requirements:

**UI Components:**
1. Add a login icon (using lucide-react icons) to the existing `src/components/Header.tsx` component. if login already, show mypage icon. let use modern icon for best ui
2. When the login icon is clicked, display a modal dialog for user authentication
3. The login form should contain:
   - Name field (supporting Vietnamese and Japanese character input)
   - Password field
   - Submit button
   - Cancel/Close button
4. Use the existing design patterns with Tailwind CSS, card-based layout, and smooth transitions consistent with the current UI

**Database Changes:**
5. Add a password column to the users table using: `ALTER TABLE users ADD COLUMN password TEXT;`
6. Ensure the password field allows NULL values initially for existing users

**Functionality:**
7. Implement form validation for both name and password fields
8. Handle login authentication by checking the name and password against the users table
9. Provide appropriate success/error feedback to the user
10. Store login state using localStorage with a custom React hook (following existing patterns)
11. Handle graceful error cases (database connection issues, invalid credentials, etc.)

**Technical Requirements:**
- Follow camelCase naming convention
- Use existing database connection patterns
- Implement proper error handling
- Ensure the modal can be closed by clicking outside or pressing ESC
- Make the login state persistent across page refreshes

✅ Implementation Summary
Authentication Hook:  src/hooks/useAuth.ts
Manages login state with localStorage persistence
Provides login(), logout(), isAuthenticated, and  user data
Follows existing patterns from useCustomerName.ts
Graceful error handling for localStorage issues
com-auth-token: authenticated
user :  {id : "65bf5917-41c0-4c6f-9f69-07a26ee83776", name : "Mạnh Công"}
