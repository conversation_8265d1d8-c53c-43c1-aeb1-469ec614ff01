I want to implement a simple login feature for the an-com-dashboard application with the following requirements:

**UI Components:**
1. Add a login icon (using lucide-react icons) to the existing `src/components/Header.tsx` component. if login already, show mypage icon. let use modern icon for best ui
2. When the login icon is clicked, display a modal dialog for user authentication
3. The login form should contain:
   - Name field (supporting Vietnamese and Japanese character input)
   - Password field
   - Submit button
   - Cancel/Close button
4. Use the existing design patterns with Tailwind CSS, card-based layout, and smooth transitions consistent with the current UI

**Database Changes:**
5. Add a password column to the users table using: `ALTER TABLE users ADD COLUMN password TEXT;`
6. Ensure the password field allows NULL values initially for existing users

**Functionality:**
7. Implement form validation for both name and password fields
8. Handle login authentication by checking the name and password against the users table
9. Provide appropriate success/error feedback to the user
10. Store login state using localStorage with a custom React hook (following existing patterns)
11. Handle graceful error cases (database connection issues, invalid credentials, etc.)

**Technical Requirements:**
- Follow camelCase naming convention
- Use existing database connection patterns
- Implement proper error handling
- Ensure the modal can be closed by clicking outside or pressing ESC
- Make the login state persistent across page refreshes

✅ Implementation Summary
Authentication Hook:  src/hooks/useAuth.ts
Manages login state with localStorage persistence
Provides login(), logout(), isAuthenticated, and  user data
Follows existing patterns from useCustomerName.ts
Graceful error handling for localStorage issues
com-auth-token: authenticated
user :  {id : "65bf5917-41c0-4c6f-9f69-07a26ee83776", name : "Mạnh Công"}


---


I want to modify the authentication system to store user data differently in localStorage and integrate it with the existing customer name functionality:

**Current Implementation in `src/hooks/useAuth.ts`:**
- Stores user object as: `{id: "65bf5917-41c0-4c6f-9f69-07a26ee83776", name: "Mạnh Công"}`
- Uses localStorage keys: `an-com-auth-user` and `an-com-auth-token`

**Requested Changes:**

1. **Update `src/hooks/useAuth.ts`:**
   - Instead of storing the complete user object in `an-com-auth-user`, split the storage into two separate localStorage keys:
     - `an-com-auth-user-id` → store the user ID (e.g., "65bf5917-41c0-4c6f-9f69-07a26ee83776")
     - `an-com-auth-user-name` → store the user name (e.g., "Mạnh Công")
   - Update all related functions (login, logout, getStoredUser, setStoredUser) to work with this new storage pattern
   - Maintain the same interface and functionality, just change the internal storage mechanism

2. **Update `src/hooks/useCustomerName.ts`:**
   - Modify the hook to check for `an-com-auth-user-name` in localStorage
   - If a user is logged in, automatically use their authenticated name as the default customer name
   - This should provide seamless integration between the authentication system and the order placement flow

3. **Update `src/components/OrderConfirmationDialog.tsx`:**
   - Ensure the dialog automatically pre-fills the customer name field with the authenticated user's name when they are logged in
   - The existing `useCustomerName` hook should handle this automatically once updated

**Goal:** Create a seamless experience where logged-in users don't need to manually enter their name when placing orders, as it will be automatically populated from their authentication data.