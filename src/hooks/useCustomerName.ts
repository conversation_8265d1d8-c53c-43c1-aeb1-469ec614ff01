import { useState, useEffect, useCallback } from "react";

// const CUSTOMER_NAME_KEY = "food-order-customer-name";
const CUSTOMER_NAME_KEY = "an-com-auth-user";
// an-com-auth-user
export interface UseCustomerNameReturn {
  customerName: string;
  setCustomerName: (name: string) => void;
  loadCustomerName: () => void;
  saveCustomerName: (name: string) => void;
  clearCustomerName: () => void;
  isLoaded: boolean;
}

type AuthUser = {
  id : string;
  name: string
}
/**
 * Custom hook for managing customer name persistence in localStorage
 * Provides auto-fill functionality and graceful error handling
 */
export function useCustomerName(): UseCustomerNameReturn {
  const [customerName, setCustomerNameState] = useState<AuthUser>({id:null, name: ''});
  const [isLoaded, setIsLoaded] = useState<boolean>(false);

  /**
   * Safely get value from localStorage
   */
  const getStoredCustomerName = useCallback((): AuthUser | null => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return null;
      }
      
      // const stored = localStorage.getItem(CUSTOMER_NAME_KEY);

      const stored = localStorage.getItem(CUSTOMER_NAME_KEY);
      if (!stored || stored === "null" || stored === "undefined") {
        // console.log('user not save in storage')
        return null;
      }

      const parsedUser = JSON.parse(stored);
      console.log("%c 🛶: functionuseCustomerName -> parsedUser ", "font-size:16px;background-color:#5c19f9;color:white;", parsedUser)

      return parsedUser && parsedUser.name ? parsedUser : null;
    } catch (error) {
      console.warn("Failed to read customer name from localStorage:", error);
      return null;
    }
  }, []);

  /**
   * Safely set value in localStorage
   */
  const setStoredCustomerName = useCallback((name: string): void => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return;
      }
      
      if (name && name.trim()) {
        localStorage.setItem(CUSTOMER_NAME_KEY, name.trim());
      } else {
        localStorage.removeItem(CUSTOMER_NAME_KEY);
      }
    } catch (error) {
      console.warn("Failed to save customer name to localStorage:", error);
    }
  }, []);

  /**
   * Load customer name from localStorage on mount
   */
  // useEffect(() => {
  //   const storedName = getStoredCustomerName();
  //   setCustomerNameState(id: '', name: );
  //   setIsLoaded(true);
  // }, [getStoredCustomerName]);

  // load when dialog open
  const loadCustomerName = useCallback(() => {
    const userName = getStoredCustomerName();
    setCustomerNameState((rev) => (id: ...rev.id, name: userName));
    setIsLoaded(true);
  }, [getStoredCustomerName]);

  /**
   * Update customer name in state (for form input)
   */
  const setCustomerName = useCallback((name: string) => {
    setCustomerNameState(name);
  }, []);

  /**
   * Save customer name to localStorage (called after successful order)
   */
  const saveCustomerName = useCallback(({id, name}) => {
    const trimmedName = name.trim();
    if (trimmedName) {
      setStoredCustomerName(trimmedName);
      setCustomerNameState(trimmedName);
    }
  }, [setStoredCustomerName]);

  /**
   * Clear customer name from both state and localStorage
   */
  const clearCustomerName = useCallback(() => {
    setCustomerNameState("");
    setStoredCustomerName("");
  }, [setStoredCustomerName]);

  return {
    customerName,
    loadCustomerName,
    setCustomerName,
    saveCustomerName,
    clearCustomerName,
    isLoaded,
  };
}
