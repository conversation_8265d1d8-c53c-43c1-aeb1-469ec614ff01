import { useState, useCallback, useEffect } from "react";

const AUTH_USER_KEY = "an-com-auth-user";
const AUTH_TOKEN_KEY = "an-com-auth-token";

export interface AuthUser {
  id: string;
  name: string;
}

export interface UseAuthReturn {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoaded: boolean;
  login: (user: AuthUser) => void;
  logout: () => void;
}

/**
 * Custom hook for managing authentication state with localStorage persistence
 * Provides login/logout functionality and graceful error handling
 */
export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);

  /**
   * Safely get user from localStorage
   */
  const getStoredUser = useCallback((): AuthUser | null => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return null;
      }
      
      const stored = localStorage.getItem(AUTH_USER_KEY);
      if (!stored || stored === "null" || stored === "undefined") {
        return null;
      }
      
      const parsedUser = JSON.parse(stored);
      
      // Validate user object structure
      if (parsedUser && typeof parsedUser === 'object' && parsedUser.id && parsedUser.name) {
        return parsedUser as AuthUser;
      }
      
      return null;
    } catch (error) {
      console.warn("Failed to read auth user from localStorage:", error);
      return null;
    }
  }, []);

  /**
   * Safely set user in localStorage
   */
  const setStoredUser = useCallback((authUser: AuthUser | null): void => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return;
      }
      
      if (authUser) {
        localStorage.setItem(AUTH_USER_KEY, JSON.stringify(authUser));
        localStorage.setItem(AUTH_TOKEN_KEY, "authenticated"); // Simple token for now
      } else {
        localStorage.removeItem(AUTH_USER_KEY);
        localStorage.removeItem(AUTH_TOKEN_KEY);
      }
    } catch (error) {
      console.warn("Failed to save auth user to localStorage:", error);
    }
  }, []);

  /**
   * Load user from localStorage on mount
   */
  useEffect(() => {
    const storedUser = getStoredUser();
    setUser(storedUser);
    setIsLoaded(true);
  }, [getStoredUser]);

  /**
   * Login user and persist to localStorage
   */
  const login = useCallback((authUser: AuthUser) => {
    setUser(authUser);
    setStoredUser(authUser);
  }, [setStoredUser]);

  /**
   * Logout user and clear localStorage
   */
  const logout = useCallback(() => {
    setUser(null);
    setStoredUser(null);
  }, [setStoredUser]);

  const isAuthenticated = user !== null;

  return {
    user,
    isAuthenticated,
    isLoaded,
    login,
    logout,
  };
}
