import { supabase } from "@/integrations/supabase/client";
import { AuthUser } from "@/hooks/useAuth";

export interface LoginCredentials {
  name: string;
  password: string;
}

export interface LoginResult {
  success: boolean;
  user?: AuthUser;
  error?: string;
}

export const authService = {
  /**
   * Authenticate user with name and password
   * @param credentials - User login credentials
   * @returns Promise with login result
   */
  login: async (credentials: LoginCredentials): Promise<LoginResult> => {
    try {
      const { name, password } = credentials;

      // Validate input
      if (!name || !name.trim()) {
        return {
          success: false,
          error: "Tên không được để trống"
        };
      }

      if (!password || !password.trim()) {
        return {
          success: false,
          error: "Mật khẩu không được để trống"
        };
      }

      // Query user from database
      const { data: users, error: queryError } = await supabase
        .from('users')
        .select('id, name, password')
        .eq('name', name.trim())
        .is('deleted_at', null)
        .limit(1);

      if (queryError) {
        console.error("Database query error:", queryError);
        return {
          success: false,
          error: "Lỗi kết nối cơ sở dữ liệu"
        };
      }

      if (!users || users.length === 0) {
        return {
          success: false,
          error: "Tên người dùng không tồn tại"
        };
      }

      const user = users[0];

      // Check if user has a password set
      if (!user.password) {
        return {
          success: false,
          error: "Tài khoản chưa được thiết lập mật khẩu"
        };
      }

      // Simple password comparison (in production, this should be hashed)
      if (user.password !== password.trim()) {
        return {
          success: false,
          error: "Mật khẩu không chính xác"
        };
      }

      // Successful login
      const authUser: AuthUser = {
        id: user.id,
        name: user.name
      };

      return {
        success: true,
        user: authUser
      };

    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Đã xảy ra lỗi không xác định"
      };
    }
  },

  /**
   * Register a new user or update existing user's password
   * @param credentials - User registration credentials
   * @returns Promise with registration result
   */
  register: async (credentials: LoginCredentials): Promise<LoginResult> => {
    try {
      const { name, password } = credentials;

      // Validate input
      if (!name || !name.trim()) {
        return {
          success: false,
          error: "Tên không được để trống"
        };
      }

      if (!password || !password.trim()) {
        return {
          success: false,
          error: "Mật khẩu không được để trống"
        };
      }

      if (password.trim().length < 4) {
        return {
          success: false,
          error: "Mật khẩu phải có ít nhất 4 ký tự"
        };
      }

      // Check if user already exists
      const { data: existingUsers, error: queryError } = await supabase
        .from('users')
        .select('id, name, password')
        .eq('name', name.trim())
        .is('deleted_at', null)
        .limit(1);

      if (queryError) {
        console.error("Database query error:", queryError);
        return {
          success: false,
          error: "Lỗi kết nối cơ sở dữ liệu"
        };
      }

      let user;

      if (existingUsers && existingUsers.length > 0) {
        // Update existing user's password
        const existingUser = existingUsers[0];
        
        const { data: updatedUsers, error: updateError } = await supabase
          .from('users')
          .update({ password: password.trim() })
          .eq('id', existingUser.id)
          .select('id, name')
          .single();

        if (updateError) {
          console.error("Database update error:", updateError);
          return {
            success: false,
            error: "Không thể cập nhật mật khẩu"
          };
        }

        user = updatedUsers;
      } else {
        // Create new user
        const { data: newUsers, error: insertError } = await supabase
          .from('users')
          .insert([{ name: name.trim(), password: password.trim() }])
          .select('id, name')
          .single();

        if (insertError) {
          console.error("Database insert error:", insertError);
          return {
            success: false,
            error: "Không thể tạo tài khoản mới"
          };
        }

        user = newUsers;
      }

      // Return successful registration
      const authUser: AuthUser = {
        id: user.id,
        name: user.name
      };

      return {
        success: true,
        user: authUser
      };

    } catch (error) {
      console.error("Registration error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Đã xảy ra lỗi không xác định"
      };
    }
  }
};
