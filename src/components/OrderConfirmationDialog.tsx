import { useState, useEffect } from "react";
import { FoodItem, OrderItem } from "@/types/food";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { ShoppingCart, User, DollarSign, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { orderService } from "@/services/orderService";
import { useCustomerName } from "@/hooks/useCustomerName";
import { useOrderHistoryActions } from "@/hooks/useOrderHistory";

interface OrderConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedItems: FoodItem[];
  onConfirmOrder: (customerName: string, items: OrderItem[], total: number) => void;
}

export function OrderConfirmationDialog({
  isOpen,
  onClose,
  selectedItems,
  onConfirmOrder
}: OrderConfirmationDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const {
    customerName,
    setCustomerName,
    saveCustomerName,
    isLoaded,
    loadCustomerName
  } = useCustomerName();
  const { addOrderPlacedEvent} = useOrderHistoryActions(customerName);

  // Reset customer name when dialog closes
  useEffect(() => {
    loadCustomerName();
    if (!isOpen) {
      // Don't clear the customer name when dialog closes
      // The saved name should persist for next time
    }
  }, [isOpen]);

  const orderItems: OrderItem[] = selectedItems.map(item => ({
    foodItem: item,
    quantity: 1
  }));

  const totalAmount = selectedItems.reduce((sum, item) => sum + item.price, 0);

  const handleConfirm = async () => {
    if (!customerName.trim()) {
      toast({
        variant: "destructive",
        title: "Lỗi",
        description: "Vui lòng nhập tên người đặt!"
      });
      return;
    }

    setIsLoading(true);

    try {
      // Save to Supabase database
      const result = await orderService.saveOrder(customerName.trim(), orderItems, totalAmount);

      if (result.success && result.order) {
        const trimmedName = customerName.trim();

        // Save customer name to localStorage for future use
        saveCustomerName(id:'', name: saveCustomerName);

        // Add to order history
        await addOrderPlacedEvent(result.order.id, totalAmount, result.order.items);

        // Call the original callback for backward compatibility
        onConfirmOrder(trimmedName, orderItems, totalAmount);

        // Close dialog (keep customer name for next time)
        onClose();

        // Show success message
        toast({
          title: "Đặt cơm thành công! 🍱",
          description: result.error
            ? `Đơn hàng của ${trimmedName} đã được lưu (${result.error})`
            : `Đơn hàng của ${trimmedName} đã được ghi nhận vào cơ sở dữ liệu.`
        });
      } else {
        // Show error message
        toast({
          variant: "destructive",
          title: "Lỗi khi lưu đơn hàng",
          description: result.error || "Không thể lưu đơn hàng. Vui lòng thử lại."
        });
      }
    } catch (error) {
      console.error("Error saving order:", error);
      toast({
        variant: "destructive",
        title: "Lỗi hệ thống",
        description: "Đã xảy ra lỗi không mong muốn. Vui lòng thử lại."
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number) => `¥${price}`;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md bg-gradient-card">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <ShoppingCart className="h-5 w-5 text-primary" />
            Xác Nhận Đơn Hàng
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Summary */}
          <div className="space-y-3">
            <h3 className="font-medium text-foreground flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-primary" />
              Chi tiết đơn hàng:
            </h3>
            
            <div className="space-y-2 bg-muted/50 p-3 rounded-lg">
              {orderItems.map((orderItem) => (
                <div key={orderItem.foodItem.id} className="flex justify-between text-sm">
                  <span>{orderItem.foodItem.name}</span>
                  <span className="font-medium text-primary">
                    {formatPrice(orderItem.foodItem.price)}
                  </span>
                </div>
              ))}
              
              <Separator className="my-2" />
              
              <div className="flex justify-between font-semibold text-base">
                <span>Tổng cộng:</span>
                <span className="text-primary text-lg">
                  {formatPrice(totalAmount)}
                </span>
              </div>
            </div>
          </div>

          {/* Customer Name Input */}
          <div className="space-y-2">
            <Label htmlFor="customerName" className="flex items-center gap-2">
              <User className="h-4 w-4 text-primary" />
              Tên người đặt:
            </Label>
            <Input
              id="customerName"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              placeholder={isLoaded ? "Nhập tên của bạn..." : "Đang tải..."}
              className="focus:ring-primary focus:border-primary"
              disabled={isLoading || !isLoaded}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isLoading}
            >
              Hủy
            </Button>
            <Button
              onClick={handleConfirm}
              className="flex-1 bg-gradient-primary hover:opacity-90"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang lưu...
                </>
              ) : (
                "Đặt Món"
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}